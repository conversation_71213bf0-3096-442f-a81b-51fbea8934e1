// FormRender Component Styles
// Professional styling using Radix UI and project design system

@import "../../assets/styles/variables";

.form-render__container {
  max-width: 100%;
  margin: 0 auto;
  font-family: $type1;
  color: $body-color;
}

// Checklist Section
.form-render__checklist-section {
  margin-bottom: 2rem;
  background: $white;
  border-radius: $card-border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-render__section-title {
  font-size: 1.25rem;
  font-weight: $font-weight-bold;
  color: $card-title-color;
  margin: 0;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid $border-color;
}

// Tabs Container
.form-render__tabs {
  width: 100%;
}

// Scrollable Tabs Area
.form-render__tabs-scroll {
  width: 100%;
  overflow: hidden;
  padding: 0 1.5rem;
  margin-top: 1rem;
}

.form-render__tabs-viewport {
  width: 100%;
  height: 100%;
}

.form-render__tabs-list {
  display: flex;
  gap: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid $border-color;
  margin-bottom: 1.5rem;
}

// Tab Triggers
.form-render__tab-trigger {
  all: unset;
  font-family: inherit;
  padding: 0.75rem 1.25rem;
  border-radius: $border-radius;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  border: 2px solid transparent;
  position: relative;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:focus {
    outline: 2px solid $primary;
    outline-offset: 2px;
  }

  &[data-state="active"] {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }
}

.form-render__tab-trigger--checked {
  background: linear-gradient(135deg, $success 0%, darken($success, 10%) 100%);
  color: $white;
  border-color: $success;

  &:hover {
    background: linear-gradient(135deg, darken($success, 5%) 0%, darken($success, 15%) 100%);
  }
}

.form-render__tab-trigger--unchecked {
  background: linear-gradient(135deg, $danger 0%, darken($danger, 10%) 100%);
  color: $white;
  border-color: $danger;

  &:hover {
    background: linear-gradient(135deg, darken($danger, 5%) 0%, darken($danger, 15%) 100%);
  }
}

// Scrollbar
.form-render__scrollbar {
  display: flex;
  user-select: none;
  touch-action: none;
  padding: 2px;
  background: $gray-lighter;
  border-radius: 4px;

  &[data-orientation="horizontal"] {
    flex-direction: column;
    height: 8px;
  }
}

.form-render__scrollbar-thumb {
  flex: 1;
  background: $gray-light;
  border-radius: 4px;
  position: relative;

  &:hover {
    background: $gray;
  }
}

// Tab Content
.form-render__tab-content {
  padding: 0 1.5rem 1.5rem;

  &:focus {
    outline: none;
  }
}

// Questions Container
.form-render__questions-container {
  width: 100%;
}

.form-render__questions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Question Cards
.form-render__question-card {
  background: $white;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.form-render__question-label {
  font-size: 1rem;
  font-weight: $font-weight-medium;
  color: $body-color;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

// Question Options
.form-render__question-options {
  margin-bottom: 1rem;
}

.form-render__selected-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: lighten($success, 45%);
  border: 1px solid lighten($success, 25%);
  border-radius: $border-radius;
  font-weight: $font-weight-medium;
  color: darken($success, 20%);
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-render__option-indicator {
  color: $success;
  font-weight: bold;
  font-size: 1rem;
}

// Remarks Section
.form-render__question-remarks {
  border-top: 1px solid $border-color;
  padding-top: 0.75rem;
}

.form-render__remarks-label {
  font-size: 0.875rem;
  font-weight: $font-weight-bold;
  color: $gray;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-render__remarks-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: $body-color;
  margin: 0;
  padding: 0.75rem;
  background: $gray-lightest;
  border-radius: $border-radius;
  border-left: 3px solid $info;
}

// No Inspection State
.form-render__no-inspection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: lighten($warning, 45%);
  border: 2px dashed lighten($warning, 20%);
  border-radius: $border-radius;
  margin: 1rem 0;
}

.form-render__no-inspection-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.form-render__no-inspection-text {
  font-size: 1.125rem;
  font-weight: $font-weight-medium;
  color: darken($warning, 30%);
  line-height: 1.4;
}

// Image Gallery
.form-render__image-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.form-render__image-item {
  width: 100%;
  height: auto;
  border-radius: $border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }
}

// Form Items
.form-render__form-item {
  background: $white;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 1.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.form-render__form-label {
  font-size: 0.875rem;
  font-weight: $font-weight-bold;
  color: $gray;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-render__form-value {
  font-size: 1rem;
  line-height: 1.5;
  color: $body-color;
  padding: 0.75rem;
  background: $gray-lightest;
  border-radius: $border-radius;
  border-left: 3px solid $primary;
  font-weight: $font-weight-medium;
}

// Signature Styles
.form-render__signature-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 1rem;
  background: $gray-lightest;
  border-radius: $border-radius;
  border: 2px dashed $border-color;
}

.form-render__signature-image {
  max-width: 200px;
  max-height: 100px;
  width: auto;
  height: auto;
  border-radius: $border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: $white;
  padding: 0.5rem;
}

// Tooltip Styles
.form-render__tooltip {
  background: $gray-dark;
  color: $white;
  padding: 0.5rem 0.75rem;
  border-radius: $border-radius;
  font-size: 0.75rem;
  font-weight: $font-weight-medium;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-width: 200px;
  text-align: center;
}

.form-render__tooltip-arrow {
  fill: $gray-dark;
}

// Responsive Design
@media (max-width: 768px) {
  .form-render__tabs-list {
    flex-wrap: wrap;
  }

  .form-render__tab-trigger {
    font-size: 0.8rem;
    padding: 0.6rem 1rem;
  }

  .form-render__section-title {
    font-size: 1.125rem;
    padding: 1rem 1rem 0.75rem;
  }

  .form-render__tab-content {
    padding: 0 1rem 1rem;
  }

  .form-render__question-card {
    padding: 1rem;
  }

  .form-render__no-inspection {
    padding: 2rem 1rem;
  }

  .form-render__no-inspection-icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .form-render__image-list {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .form-render__tabs-scroll {
    padding: 0 1rem;
  }
}
