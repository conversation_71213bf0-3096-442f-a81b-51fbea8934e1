import React, { useState } from 'react';
import * as Tabs from '@radix-ui/react-tabs';
import * as Scroll<PERSON><PERSON> from '@radix-ui/react-scroll-area';
import * as Tooltip from '@radix-ui/react-tooltip';
import { STATIC_URL } from '../constants';
import './FormRender.module.scss';

const FormRender = ({ formData }) => {
    // Collect all checklist-group items
    const checklistGroups = formData.filter(item => item.type === 'checklist-group');
    const [activeTab, setActiveTab] = useState(checklistGroups.length > 0 ? checklistGroups[0].label : '');

    const renderImages = (imageUrls) => {
        if (imageUrls) {
            const urls = imageUrls.split(', ');
            return (
                <ul className="form-render__image-list">
                    {urls.map((url, index) => (
                        <li key={index}>
                            <img
                                src={`${STATIC_URL}/${url}`}
                                alt={`Uploaded content ${index + 1}`}
                                className="form-render__image-item"
                            />
                        </li>
                    ))}
                </ul>
            );
        }
        return null;
    };

    return (
        <div className="form-render__container">
            {/* Render checklist groups as tabs if they exist */}
            {checklistGroups.length > 0 && (
                <div className="form-render__checklist-section">
                    <h4 className="form-render__section-title">Inspection Checklist</h4>
                    <Tooltip.Provider>
                        <Tabs.Root
                            value={activeTab}
                            onValueChange={setActiveTab}
                            className="form-render__tabs"
                        >
                            <ScrollArea.Root className="form-render__tabs-scroll">
                                <ScrollArea.Viewport className="form-render__tabs-viewport">
                                    <Tabs.List className="form-render__tabs-list">
                                        {checklistGroups.map((group, index) => {
                                            const label = group.label || 'No Label';
                                            const isChecked = group.checked;

                                            return (
                                                <Tooltip.Root key={index}>
                                                    <Tooltip.Trigger asChild>
                                                        <Tabs.Trigger
                                                            value={label}
                                                            className={`form-render__tab-trigger ${
                                                                isChecked
                                                                    ? 'form-render__tab-trigger--checked'
                                                                    : 'form-render__tab-trigger--unchecked'
                                                            }`}
                                                        >
                                                            <span dangerouslySetInnerHTML={{ __html: label }} />
                                                        </Tabs.Trigger>
                                                    </Tooltip.Trigger>
                                                    <Tooltip.Portal>
                                                        <Tooltip.Content
                                                            className="form-render__tooltip"
                                                            sideOffset={5}
                                                        >
                                                            {isChecked ? 'Inspection completed' : 'No inspection carried out'}
                                                            <Tooltip.Arrow className="form-render__tooltip-arrow" />
                                                        </Tooltip.Content>
                                                    </Tooltip.Portal>
                                                </Tooltip.Root>
                                            );
                                        })}
                                    </Tabs.List>
                                </ScrollArea.Viewport>
                                <ScrollArea.Scrollbar
                                    className="form-render__scrollbar"
                                    orientation="horizontal"
                                >
                                    <ScrollArea.Thumb className="form-render__scrollbar-thumb" />
                                </ScrollArea.Scrollbar>
                            </ScrollArea.Root>

                            {checklistGroups.map((group, index) => {
                                const label = group.label || 'No Label';
                                const questions = group.questions || [];
                                const isChecked = group.checked;

                                return (
                                    <Tabs.Content
                                        key={index}
                                        value={label}
                                        className="form-render__tab-content"
                                    >
                                        {isChecked ? (
                                            <div className="form-render__questions-container">
                                                {questions.length > 0 && (
                                                    <div className="form-render__questions-list">
                                                        {questions.map((question, qIndex) => {
                                                            const questionLabel = question.label || 'No Question Label';
                                                            const options = question.options || [];

                                                            return (
                                                                <div key={qIndex} className="form-render__question-card">
                                                                    <div className="form-render__question-label">
                                                                        Q{qIndex + 1}. {questionLabel}
                                                                    </div>
                                                                    {options.length > 0 && (
                                                                        <div className="form-render__question-options">
                                                                            {options.map((option, oIndex) => {
                                                                                if (option.checked === 1) {
                                                                                    return (
                                                                                        <div key={oIndex} className="form-render__selected-option">
                                                                                            <span className="form-render__option-indicator">✓</span>
                                                                                            {option.label}
                                                                                        </div>
                                                                                    );
                                                                                } else {
                                                                                    return null;
                                                                                }
                                                                            })}
                                                                        </div>
                                                                    )}

                                                                    {question.remarks && (
                                                                        <div className="form-render__question-remarks">
                                                                            <div className="form-render__remarks-label">Remarks</div>
                                                                            <p className="form-render__remarks-text">{question.remarks}</p>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                        ) : (
                                            <div className="form-render__no-inspection">
                                                <div className="form-render__no-inspection-icon">⚠️</div>
                                                <div className="form-render__no-inspection-text">
                                                    No Inspection carried out in this section
                                                </div>
                                            </div>
                                        )}
                                    </Tabs.Content>
                                );
                            })}
                        </Tabs.Root>
                    </Tooltip.Provider>
                </div>
            )}

            {/* Render other form items */}
            {formData.map((item, index) => {
                if (item.type === "textarea") {
                    return (
                        <div key={index} className="form-render__form-item">
                            <div className="form-render__form-label">{item.label}</div>
                            <div className="form-render__form-value">{item.value || ''}</div>
                        </div>
                    )
                } else if (item.type === "sign") {
                    return (
                        <div key={index} className="form-render__form-item">
                            <div className="form-render__form-label">{item.label}</div>
                            <div className="form-render__signature-container">
                                <img
                                    src={`${STATIC_URL}/${item.value}`}
                                    alt="Signature"
                                    className="form-render__signature-image"
                                />
                            </div>
                        </div>
                    )
                }
                return null;
            })}
        </div>
    );
};

export default FormRender;
